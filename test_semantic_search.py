#!/usr/bin/env python3
"""
Test script cho semantic search functionality
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1/pdf"

def test_health():
    """Test health endpoint"""
    print("=== TESTING HEALTH ENDPOINT ===")
    try:
        response = requests.get(f"{BASE_URL}/health")
        data = response.json()
        
        print(f"Status: {data.get('status')}")
        print(f"Embedding Model: {data.get('embedding_model', 'Not found')}")
        print(f"Vector Dimension: {data.get('vector_dimension', 'Not found')}")
        print(f"Qdrant Status: {data.get('qdrant_status', 'Not found')}")
        print(f"Vector Search: {data.get('services', {}).get('vector_search', 'Not found')}")
        
        return data.get('services', {}).get('vector_search') == 'available'
        
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_semantic_search():
    """Test semantic search endpoint"""
    print("\n=== TESTING SEMANTIC SEARCH ===")
    
    # Test cases
    test_queries = [
        {
            "query": "nguyên tử",
            "description": "Basic Vietnamese chemistry term"
        },
        {
            "query": "định nghĩa nguyên tử",
            "semantic_tags": "definition",
            "description": "Search for definitions"
        },
        {
            "query": "ví dụ về liên kết hóa học",
            "has_examples": True,
            "description": "Search for examples"
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n--- Test Case {i}: {test_case['description']} ---")
        
        # Prepare parameters
        params = {
            "query": test_case["query"],
            "limit": 3
        }
        
        # Add optional parameters
        if "semantic_tags" in test_case:
            params["semantic_tags"] = test_case["semantic_tags"]
        if "has_examples" in test_case:
            params["has_examples"] = test_case["has_examples"]
        
        try:
            response = requests.get(f"{BASE_URL}/search-semantic", params=params)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Success: Found {len(data.get('results', []))} results")
                
                if data.get('results'):
                    for j, result in enumerate(data['results'][:2], 1):
                        print(f"  Result {j}:")
                        print(f"    Score: {result.get('score', 0):.3f}")
                        print(f"    Text: {result.get('text', '')[:100]}...")
                        print(f"    Semantic Tags: {result.get('semantic_tags', [])}")
                else:
                    print("  No results found")
                    
            else:
                print(f"❌ Failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_regular_search():
    """Test regular search for comparison"""
    print("\n=== TESTING REGULAR SEARCH ===")
    
    try:
        params = {"query": "nguyên tử", "limit": 3}
        response = requests.get(f"{BASE_URL}/search", params=params)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Regular search: Found {len(data.get('results', []))} results")
            
            if data.get('results'):
                for i, result in enumerate(data['results'][:2], 1):
                    print(f"  Result {i}:")
                    print(f"    Score: {result.get('score', 0):.3f}")
                    print(f"    Text: {result.get('text', '')[:100]}...")
        else:
            print(f"❌ Regular search failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Regular search error: {e}")

def main():
    """Main test function"""
    print("🔧 Testing Semantic Search Functionality")
    print("=" * 50)
    
    # Test health first
    if not test_health():
        print("❌ Health check failed, stopping tests")
        return
    
    # Wait a moment for services to be ready
    print("\n⏳ Waiting for services to be ready...")
    time.sleep(2)
    
    # Test searches
    test_regular_search()
    test_semantic_search()
    
    print("\n" + "=" * 50)
    print("🏁 Testing completed!")

if __name__ == "__main__":
    main()
