#!/usr/bin/env python3
"""
Fix vector dimension mismatch in Qdrant collections
"""

import requests
import json

def delete_collection(collection_name):
    """Delete a collection"""
    print(f"🗑️  Deleting collection: {collection_name}")
    
    try:
        response = requests.delete(f"http://localhost:6333/collections/{collection_name}")
        
        if response.status_code == 200:
            print(f"✅ Successfully deleted {collection_name}")
            return True
        else:
            print(f"❌ Failed to delete {collection_name}: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error deleting {collection_name}: {e}")
        return False

def recreate_collection_via_api(book_id):
    """Recreate collection via FastAPI endpoint"""
    print(f"🔧 Recreating collection for book_id: {book_id}")
    
    try:
        # Call the recreate collection endpoint
        response = requests.post(f"http://localhost:8000/api/v1/pdf/recreate-collection/{book_id}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Successfully recreated collection: {data}")
            return True
        else:
            print(f"❌ Failed to recreate collection: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error recreating collection: {e}")
        return False

def get_textbooks():
    """Get list of textbooks from MongoDB"""
    print("📚 Getting textbooks from MongoDB...")

    try:
        response = requests.get("http://localhost:8000/api/v1/pdf/textbooks")

        if response.status_code == 200:
            data = response.json()
            textbooks = data.get('textbooks', [])
            print(f"✅ Found {len(textbooks)} textbooks")
            return textbooks
        else:
            print(f"❌ Failed to get textbooks: {response.status_code}")
            return []

    except Exception as e:
        print(f"❌ Error getting textbooks: {e}")
        return []

def check_collection_after_fix(collection_name):
    """Check collection status after fix"""
    print(f"🔍 Checking collection after fix: {collection_name}")
    
    try:
        response = requests.get(f"http://localhost:6333/collections/{collection_name}")
        
        if response.status_code == 200:
            data = response.json()
            result = data.get('result', {})
            
            vectors_count = result.get('vectors_count', 0)
            points_count = result.get('points_count', 0)
            
            config = result.get('config', {})
            params = config.get('params', {})
            vectors = params.get('vectors', {})
            vector_size = vectors.get('size', 'Unknown') if isinstance(vectors, dict) else 'Unknown'
            
            print(f"  Status: {result.get('status', 'Unknown')}")
            print(f"  Points: {points_count}")
            print(f"  Vectors: {vectors_count}")
            print(f"  Vector size: {vector_size}")
            
            return vectors_count > 0 and vector_size == 768
        else:
            print(f"❌ Collection not found: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error checking collection: {e}")
        return False

def main():
    """Main fix function"""
    print("🔧 Fixing Vector Dimension Mismatch")
    print("=" * 50)

    # Get textbooks from MongoDB
    textbooks = get_textbooks()

    # Collections to fix
    collections_to_fix = [
        {"name": "textbook_f62feea8", "book_id": "f62feea8"},
        {"name": "textbook_87602256", "book_id": "87602256"}
    ]

    print(f"\n⚠️  VECTOR DIMENSION MISMATCH DETECTED!")
    print(f"   Current embedding model creates 768-dimensional vectors")
    print(f"   But existing collections expect 384-dimensional vectors")
    print(f"   This causes search to return 0 results.")
    print(f"\n🔧 SOLUTION: Delete old collections and re-import textbooks")

    for collection_info in collections_to_fix:
        collection_name = collection_info["name"]
        book_id = collection_info["book_id"]

        print(f"\n--- Deleting {collection_name} ---")

        # Delete old collection with wrong vector dimensions
        if delete_collection(collection_name):
            print(f"✅ {collection_name} deleted successfully!")
        else:
            print(f"❌ Failed to delete {collection_name}")

    print("\n" + "=" * 50)
    print("🏁 Collections deleted!")
    print("\n📋 NEXT STEPS:")
    print("1. Use the textbook import endpoint to re-import your textbooks")
    print("2. This will create new collections with correct 768-dimensional vectors")
    print("3. Then semantic search will work properly")

    if textbooks:
        print(f"\n📚 Available textbooks to re-import:")
        for book in textbooks:
            book_id = book.get('_id', 'Unknown')
            title = book.get('title', 'Unknown')
            print(f"   - {book_id}: {title}")

    print(f"\n💡 Example: Use the PDF processing endpoint to re-import textbooks")

if __name__ == "__main__":
    main()
