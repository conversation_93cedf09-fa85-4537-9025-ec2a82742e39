#!/usr/bin/env python3
"""
Simple script to delete Qdrant collections with wrong vector dimensions
"""

import requests

def main():
    print("🗑️  Deleting collections with wrong vector dimensions...")
    
    collections = ['textbook_f62feea8', 'textbook_87602256']
    
    for collection in collections:
        print(f"\nDeleting {collection}...")
        try:
            response = requests.delete(f'http://localhost:6333/collections/{collection}')
            if response.status_code == 200:
                print(f'✅ {collection} deleted successfully')
            else:
                print(f'❌ Failed to delete {collection}: {response.status_code}')
                print(f'   Response: {response.text}')
        except Exception as e:
            print(f'❌ Error deleting {collection}: {e}')
    
    print("\n" + "=" * 50)
    print("🏁 Collections deleted!")
    print("\n⚠️  IMPORTANT: Vector dimension mismatch fixed!")
    print("   - Old collections had 384-dimensional vectors")
    print("   - Current embedding model creates 768-dimensional vectors")
    print("   - This was causing search to return 0 results")
    print("\n📋 NEXT STEPS:")
    print("1. Re-import your textbooks using the PDF processing endpoint")
    print("2. New collections will be created with correct 768-dimensional vectors")
    print("3. Semantic search will then work properly")

if __name__ == "__main__":
    main()
