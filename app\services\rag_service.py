"""
RAG (Retrieval-Augmented Generation) Service
Xử lý logic RAG: semantic search + LLM generation
"""

import logging
from typing import Dict, Any, Optional, List
from app.services.enhanced_textbook_service import EnhancedTextbookService

logger = logging.getLogger(__name__)

class RAGService:
    """Service xử lý RAG workflow"""
    
    def __init__(self):
        self.textbook_service = EnhancedTextbookService()
    
    async def process_rag_query(
        self,
        query: str,
        book_id: Optional[str] = None,
        lesson_id: Optional[str] = None,
        limit: int = 5,
        semantic_tags: Optional[str] = None,
        temperature: float = 0.3,
        max_tokens: int = 2000
    ) -> Dict[str, Any]:
        """
        Xử lý RAG query hoàn chỉnh
        
        Args:
            query: Câu hỏi của người dùng
            book_id: ID sách cụ thể (tùy chọn)
            lesson_id: ID bài học cụ thể (tùy chọn)
            limit: Số lượng kết quả tìm kiếm tối đa
            semantic_tags: Lọc theo semantic tags
            temperature: Temperature cho LLM response
            max_tokens: Số token tối đa cho response
            
        Returns:
            Dict chứa kết quả RAG với answer đã được làm sạch
        """
        try:
            logger.info(f"Processing RAG query: {query[:100]}...")
            
            # Kiểm tra LLM service
            from app.services.openrouter_service import OpenRouterService
            llm_service = OpenRouterService()
            
            if not llm_service.available:
                return {
                    "success": False,
                    "error": "LLM service không khả dụng. Vui lòng kiểm tra cấu hình API key."
                }
            
            # Step 1: Semantic search để tìm context
            search_result = await self._perform_semantic_search(
                query=query,
                book_id=book_id,
                lesson_id=lesson_id,
                limit=limit,
                semantic_tags=semantic_tags
            )
            
            if not search_result.get("success"):
                return {
                    "success": False,
                    "error": f"Search failed: {search_result.get('error')}"
                }
            
            search_results = search_result.get("results", [])
            
            if not search_results:
                return {
                    "success": True,
                    "query": query,
                    "answer": "Xin lỗi, tôi không tìm thấy thông tin liên quan đến câu hỏi của bạn trong tài liệu.",
                    "sources": [],
                    "search_results_count": 0,
                    "filters_applied": self._build_filters_info(semantic_tags, lesson_id, book_id)
                }
            
            # Step 2: Tạo context từ search results
            context, sources = self._build_context_and_sources(search_results, book_id)
            
            # Step 3: Tạo prompt cho LLM
            rag_prompt = self._build_rag_prompt(context, query)
            
            # Step 4: Gọi LLM
            llm_result = await llm_service.generate_content(
                prompt=rag_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            if not llm_result.get("success"):
                return {
                    "success": False,
                    "error": f"LLM generation failed: {llm_result.get('error')}"
                }
            
            # Step 5: Làm sạch answer text
            raw_answer = llm_result.get("text", "")
            clean_answer = self.textbook_service.clean_text_content(raw_answer)
            
            return {
                "success": True,
                "query": query,
                "answer": clean_answer,
                "sources": sources,
                "search_results_count": len(search_results),
                "filters_applied": self._build_filters_info(semantic_tags, lesson_id, book_id)
            }
            
        except Exception as e:
            logger.error(f"RAG query failed: {e}")
            return {
                "success": False,
                "error": f"RAG query failed: {str(e)}"
            }
    
    async def _perform_semantic_search(
        self,
        query: str,
        book_id: Optional[str],
        lesson_id: Optional[str],
        limit: int,
        semantic_tags: Optional[str]
    ) -> Dict[str, Any]:
        """Thực hiện semantic search"""
        from app.services.qdrant_service import qdrant_service
        
        search_params = {
            "query": query,
            "limit": limit
        }
        
        # Thêm filters nếu có
        semantic_filters = {}
        if semantic_tags:
            semantic_filters["semantic_tags"] = semantic_tags.split(",")
        
        # Thêm lesson_id filter nếu có
        if lesson_id:
            semantic_filters["lesson_id"] = lesson_id
        
        if semantic_filters:
            search_params["semantic_filters"] = semantic_filters
        
        # Tìm kiếm trong book cụ thể hoặc toàn bộ
        if book_id:
            return await qdrant_service.search_textbook(book_id, **search_params)
        else:
            # Global search across all books
            return await qdrant_service.global_search(**search_params)
    
    def _build_context_and_sources(self, search_results: List[Dict], book_id: Optional[str]) -> tuple:
        """Tạo context và sources từ search results"""
        context_parts = []
        sources = []
        
        for i, result in enumerate(search_results, 1):
            context_parts.append(f"[Nguồn {i}] {result.get('text', '')}")
            sources.append({
                "source_id": i,
                "text": result.get('text', '')[:200] + "..." if len(result.get('text', '')) > 200 else result.get('text', ''),
                "score": result.get('score', 0),
                "lesson_id": result.get('lesson_id', ''),
                "semantic_tags": result.get('semantic_tags', []),
                "book_id": result.get('book_id', book_id)
            })
        
        context = "\n\n".join(context_parts)
        return context, sources
    
    def _build_rag_prompt(self, context: str, query: str) -> str:
        """Tạo prompt cho LLM"""
        return f"""Bạn là một trợ lý AI chuyên về giáo dục. Hãy trả lời câu hỏi dựa trên thông tin được cung cấp.

NGUYÊN TẮC:
- Chỉ sử dụng thông tin từ các nguồn được cung cấp
- Trả lời bằng tiếng Việt, rõ ràng và dễ hiểu
- Nếu thông tin không đủ để trả lời, hãy nói rõ
- Trích dẫn nguồn khi cần thiết (ví dụ: "Theo nguồn 1...")

THÔNG TIN TỪ TÀI LIỆU:
{context}

CÂU HỎI: {query}

TRẢ LỜI:"""
    
    def _build_filters_info(self, semantic_tags: Optional[str], lesson_id: Optional[str], book_id: Optional[str]) -> Dict[str, Any]:
        """Tạo thông tin filters đã áp dụng"""
        return {
            "semantic_tags": semantic_tags.split(",") if semantic_tags else None,
            "lesson_id": lesson_id,
            "book_id": book_id
        }

# Singleton instance
rag_service = RAGService()
