#!/usr/bin/env python3
"""
Test script cho nvidia/NV-Embed-v2 embedding model
"""

import asyncio
import requests
import json
import time

async def test_nvidia_embedding():
    """Test nvidia embedding model"""
    
    print("🧪 Testing nvidia/NV-Embed-v2 Embedding Model...")
    print("=" * 60)
    
    # Test 1: Health check
    print("\n1. 🔍 Health Check...")
    try:
        response = requests.get("http://localhost:8000/api/v1/pdf/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Server Status: {health_data.get('status', 'Unknown')}")
            print(f"📊 Embedding Model: {health_data.get('embedding_model', 'Unknown')}")
            print(f"🔢 Vector Dimension: {health_data.get('vector_dimension', 'Unknown')}")
            print(f"🧠 Semantic Analysis: {health_data.get('semantic_analysis', 'Unknown')}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return
    
    # Test 2: Semantic Analysis với tiếng Việt
    print("\n2. 🧠 Testing Semantic Analysis (Vietnamese)...")
    vietnamese_content = """
    Định nghĩa nguyên tử: Nguyên tử là hạt nhỏ nhất của nguyên tố hóa học, 
    không thể phân chia được bằng các phương pháp hóa học thông thường.
    
    Ví dụ: Nguyên tử hydro (H) có 1 proton và 1 electron.
    Công thức tính khối lượng nguyên tử: A = Z + N
    Trong đó: A là số khối, Z là số proton, N là số neutron.
    """
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/pdf/test-semantic-analysis",
            params={"content": vietnamese_content},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            semantic_analysis = result.get("semantic_analysis", {})
            print("✅ Semantic Analysis Success!")
            print(f"📝 Semantic Tags: {semantic_analysis.get('semantic_tags', [])}")
            print(f"📊 Difficulty: {semantic_analysis.get('difficulty', 'N/A')}")
            print(f"🔑 Key Concepts: {semantic_analysis.get('key_concepts', [])}")
            print(f"⚙️ Analysis Method: {semantic_analysis.get('analysis_method', 'N/A')}")
        else:
            print(f"❌ Semantic analysis failed: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ Semantic analysis error: {e}")
    
    # Test 3: Simple Text Processing
    print("\n3. 📚 Testing Simple Text Processing...")
    test_text = """
    Bài 1: Cấu tạo nguyên tử
    
    Nguyên tử gồm có hạt nhân mang điện tích dương và các electron mang điện tích âm 
    chuyển động xung quanh hạt nhân. Hạt nhân nguyên tử gồm có proton và neutron.
    
    Ví dụ minh họa:
    - Nguyên tử Carbon có 6 proton, 6 neutron và 6 electron
    - Nguyên tử Oxygen có 8 proton, 8 neutron và 8 electron
    
    Công thức tính điện tích hạt nhân: Q = Z × e
    Trong đó Z là số proton, e là điện tích nguyên tố.
    """
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/pdf/process-simple-text",
            json={
                "book_id": "test_nvidia_book",
                "text_content": test_text,
                "lesson_id": "lesson_01",
                "book_title": "Test Nvidia Embedding"
            },
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Text Processing Success!")
            print(f"📖 Book ID: {result.get('book_id', 'N/A')}")
            print(f"📄 Total Chunks: {result.get('total_chunks', 'N/A')}")
            print(f"🔢 Vector Dimension: {result.get('vector_dimension', 'N/A')}")
            print(f"📊 Collection: {result.get('collection_name', 'N/A')}")
        else:
            print(f"❌ Text processing failed: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ Text processing error: {e}")
    
    # Test 4: Search Test
    print("\n4. 🔍 Testing Search with Vietnamese Query...")
    try:
        response = requests.get(
            "http://localhost:8000/api/v1/pdf/search-textbook",
            params={
                "book_id": "test_nvidia_book",
                "query": "cấu tạo nguyên tử carbon",
                "limit": 3
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Search Success!")
            print(f"📊 Found {len(result.get('results', []))} results")
            
            for i, res in enumerate(result.get('results', [])[:2]):
                print(f"\n📄 Result {i+1}:")
                print(f"   Score: {res.get('score', 'N/A'):.4f}")
                print(f"   Text: {res.get('text', 'N/A')[:100]}...")
                print(f"   Semantic Tags: {res.get('semantic_tags', [])}")
        else:
            print(f"❌ Search failed: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ Search error: {e}")
    
    # Test 5: Semantic Search
    print("\n5. 🎯 Testing Semantic Search...")
    try:
        response = requests.get(
            "http://localhost:8000/api/v1/pdf/search-semantic",
            params={
                "book_id": "test_nvidia_book",
                "query": "ví dụ về nguyên tử",
                "semantic_tags": "example,definition",
                "limit": 2
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Semantic Search Success!")
            print(f"📊 Found {len(result.get('results', []))} results")
            
            for i, res in enumerate(result.get('results', [])):
                print(f"\n📄 Result {i+1}:")
                print(f"   Score: {res.get('score', 'N/A'):.4f}")
                print(f"   Semantic Tags: {res.get('semantic_tags', [])}")
                print(f"   Text: {res.get('text', 'N/A')[:80]}...")
        else:
            print(f"❌ Semantic search failed: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ Semantic search error: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 nvidia/NV-Embed-v2 Testing Complete!")
    print("✅ Model supports Vietnamese language")
    print("✅ Embeddings are created before saving to Qdrant")
    print("✅ Semantic analysis works with Vietnamese content")

if __name__ == "__main__":
    asyncio.run(test_nvidia_embedding())
