#!/usr/bin/env python3
"""
Complete workflow test: Import textbook and test semantic search
"""

import requests
import json
import time
import os

BASE_URL = "http://localhost:8000/api/v1/pdf"

def check_health():
    """Check system health"""
    print("=== CHECKING SYSTEM HEALTH ===")
    try:
        response = requests.get(f"{BASE_URL}/health")
        data = response.json()
        
        print(f"Status: {data.get('status')}")
        print(f"Embedding Model: {data.get('embedding_model', 'Not found')}")
        print(f"Vector Dimension: {data.get('vector_dimension', 'Not found')}")
        print(f"Vector Search: {data.get('services', {}).get('vector_search', 'Not found')}")
        
        return data.get('services', {}).get('vector_search') == 'available'
        
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def check_available_pdfs():
    """Check what PDFs are available for import"""
    print("\n=== CHECKING AVAILABLE PDFs ===")
    
    pdf_folder = "data/sáchgiaokhoa"
    if os.path.exists(pdf_folder):
        pdf_files = [f for f in os.listdir(pdf_folder) if f.endswith('.pdf')]
        print(f"Found {len(pdf_files)} PDF files:")
        for pdf in pdf_files[:5]:  # Show first 5
            print(f"  - {pdf}")
        if len(pdf_files) > 5:
            print(f"  ... and {len(pdf_files) - 5} more")
        return pdf_files
    else:
        print(f"❌ PDF folder not found: {pdf_folder}")
        return []

def import_sample_textbook():
    """Import a sample textbook for testing"""
    print("\n=== IMPORTING SAMPLE TEXTBOOK ===")
    
    # Check if we have any PDFs
    pdf_files = check_available_pdfs()
    if not pdf_files:
        print("❌ No PDF files found to import")
        return None
    
    # Use the first PDF file
    sample_pdf = pdf_files[0]
    print(f"📚 Importing: {sample_pdf}")
    
    try:
        # Prepare the import request
        files = {'file': open(f"data/sáchgiaokhoa/{sample_pdf}", 'rb')}
        data = {
            'subject': 'Chemistry',
            'grade': '12',
            'title': f'Test Import - {sample_pdf}'
        }
        
        response = requests.post(f"{BASE_URL}/import-textbook", files=files, data=data)
        files['file'].close()
        
        if response.status_code == 200:
            result = response.json()
            book_id = result.get('book_id')
            print(f"✅ Import started successfully!")
            print(f"   Book ID: {book_id}")
            print(f"   Task ID: {result.get('task_id')}")
            return book_id
        else:
            print(f"❌ Import failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Import error: {e}")
        return None

def wait_for_processing(book_id, max_wait=300):
    """Wait for textbook processing to complete"""
    print(f"\n⏳ Waiting for processing to complete (max {max_wait}s)...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            # Check if collection exists and has vectors
            response = requests.get(f"http://localhost:6333/collections/textbook_{book_id}")
            
            if response.status_code == 200:
                data = response.json()
                result = data.get('result', {})
                vectors_count = result.get('vectors_count', 0)
                points_count = result.get('points_count', 0)
                
                if vectors_count > 0:
                    print(f"✅ Processing complete!")
                    print(f"   Collection: textbook_{book_id}")
                    print(f"   Points: {points_count}")
                    print(f"   Vectors: {vectors_count}")
                    return True
                else:
                    print(f"   Processing... Points: {points_count}, Vectors: {vectors_count}")
            
            time.sleep(10)  # Wait 10 seconds before checking again
            
        except Exception as e:
            print(f"   Checking... ({e})")
            time.sleep(10)
    
    print(f"⚠️  Processing timeout after {max_wait}s")
    return False

def test_semantic_search_with_book(book_id):
    """Test semantic search with the imported book"""
    print(f"\n=== TESTING SEMANTIC SEARCH WITH BOOK {book_id} ===")
    
    test_queries = [
        {
            "query": "định nghĩa",
            "description": "Search for definitions"
        },
        {
            "query": "ví dụ",
            "description": "Search for examples"
        },
        {
            "query": "công thức",
            "description": "Search for formulas"
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n--- Test {i}: {test_case['description']} ---")
        
        try:
            params = {
                "query": test_case["query"],
                "book_id": book_id,
                "limit": 3
            }
            
            response = requests.get(f"{BASE_URL}/search-semantic", params=params)
            
            if response.status_code == 200:
                data = response.json()
                results = data.get('results', [])
                print(f"✅ Found {len(results)} results")
                
                for j, result in enumerate(results[:2], 1):
                    print(f"  Result {j}:")
                    print(f"    Score: {result.get('score', 0):.3f}")
                    print(f"    Text: {result.get('text', '')[:100]}...")
                    print(f"    Semantic Tags: {result.get('semantic_tags', [])}")
                    
                return len(results) > 0
            else:
                print(f"❌ Search failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Search error: {e}")
            return False

def main():
    """Main test workflow"""
    print("🔧 Complete Workflow Test: Import + Semantic Search")
    print("=" * 60)
    
    # Step 1: Check system health
    if not check_health():
        print("❌ System not healthy, stopping test")
        return
    
    # Step 2: Import a sample textbook
    book_id = import_sample_textbook()
    if not book_id:
        print("❌ Failed to import textbook, stopping test")
        return
    
    # Step 3: Wait for processing to complete
    if not wait_for_processing(book_id):
        print("❌ Processing timeout, but continuing with test...")
    
    # Step 4: Test semantic search
    if test_semantic_search_with_book(book_id):
        print("\n✅ SEMANTIC SEARCH IS WORKING!")
    else:
        print("\n❌ Semantic search still not working")
    
    print("\n" + "=" * 60)
    print("🏁 Complete workflow test finished!")

if __name__ == "__main__":
    main()
