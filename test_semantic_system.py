"""
Script test hệ thống semantic analysis mới
"""
import asyncio
import requests
import json
from typing import Dict, Any

BASE_URL = "http://localhost:8000/api/v1"

def test_semantic_analysis_api():
    """Test API semantic analysis"""
    print("🧪 Testing Semantic Analysis API...")
    
    test_contents = [
        "Định nghĩa nguyên tử: Nguyên tử là hạt nhỏ nhất của nguyên tố hóa học, không thể phân chia được bằng các phương pháp hóa học thông thường.",
        "Ví dụ về phản ứng hóa học: 2H₂ + O₂ → 2H₂O. <PERSON><PERSON><PERSON> là phản ứng tổng hợp nước từ khí hydro và oxy.",
        "Bài tập: Tính khối lượng mol của hợp chất CaCO₃. Biết Ca = 40, C = 12, O = 16.",
        "<PERSON>ông thức tính nồng độ mol: CM = n/V, trong đó n là số mol chất tan, <PERSON> là thể tích dung dịch (lít)."
    ]
    
    for i, content in enumerate(test_contents, 1):
        print(f"\n--- Test {i} ---")
        print(f"Content: {content[:50]}...")
        
        try:
            response = requests.post(
                f"{BASE_URL}/pdf/test-semantic-analysis",
                params={"content": content},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                semantic_analysis = result.get("semantic_analysis", {})
                
                print("✅ Success!")
                print(f"Semantic Tags: {semantic_analysis.get('semantic_tags', [])}")
                print(f"Difficulty: {semantic_analysis.get('difficulty', 'N/A')}")
                print(f"Key Concepts: {semantic_analysis.get('key_concepts', [])}")
                print(f"Analysis Method: {semantic_analysis.get('analysis_method', 'N/A')}")
                
                # Kiểm tra confidence scores
                tags = semantic_analysis.get('semantic_tags', [])
                if tags:
                    avg_confidence = sum(tag.get('confidence', 0) for tag in tags) / len(tags)
                    print(f"Average Confidence: {avg_confidence:.2f}")
                
            else:
                print(f"❌ Failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_semantic_search_api():
    """Test API semantic search"""
    print("\n🔍 Testing Semantic Search API...")
    
    test_queries = [
        {
            "query": "định nghĩa nguyên tử",
            "semantic_tags": "definition",
            "difficulty": "basic"
        },
        {
            "query": "ví dụ phản ứng",
            "semantic_tags": "example",
            "has_examples": True
        },
        {
            "query": "công thức hóa học",
            "semantic_tags": "formula",
            "has_formulas": True,
            "min_confidence": 0.5
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n--- Search Test {i} ---")
        print(f"Query: {test_case['query']}")
        print(f"Filters: {json.dumps({k: v for k, v in test_case.items() if k != 'query'}, indent=2)}")
        
        try:
            response = requests.get(
                f"{BASE_URL}/pdf/search-semantic",
                params=test_case,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                results = result.get("results", [])
                
                print(f"✅ Found {len(results)} results")
                
                for j, res in enumerate(results[:2], 1):  # Show first 2 results
                    print(f"  Result {j}:")
                    print(f"    Score: {res.get('score', 0):.3f}")
                    print(f"    Semantic Tags: {res.get('semantic_tags', [])}")
                    print(f"    Text Preview: {res.get('text', '')[:100]}...")
                    
            else:
                print(f"❌ Failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_health_check():
    """Test health check với semantic info"""
    print("\n🏥 Testing Health Check...")
    
    try:
        response = requests.get(f"{BASE_URL}/pdf/health", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            services = result.get("services", {})
            
            print("✅ Health Check Success!")
            print(f"Semantic Analysis: {services.get('semantic_analysis', 'N/A')}")
            print(f"LLM Analysis: {services.get('llm_analysis', 'N/A')}")
            print(f"Vector Search: {services.get('vector_search', 'N/A')}")
            
            # Check available endpoints
            endpoints = result.get("available_endpoints", [])
            semantic_endpoints = [ep for ep in endpoints if "semantic" in ep]
            print(f"Semantic Endpoints: {semantic_endpoints}")
            
        else:
            print(f"❌ Failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

async def test_semantic_analysis_service():
    """Test semantic analysis service trực tiếp"""
    print("\n🔬 Testing Semantic Analysis Service Directly...")
    
    try:
        from app.services.semantic_analysis_service import semantic_analysis_service
        
        test_content = "Định nghĩa nguyên tử: Nguyên tử là đơn vị cơ bản của vật chất, bao gồm hạt nhân và các electron quay quanh hạt nhân."
        
        result = await semantic_analysis_service.analyze_content_semantic(test_content)
        
        print("✅ Direct Service Test Success!")
        print(f"Result: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # Validate result structure
        required_keys = ["semantic_tags", "difficulty", "key_concepts", "contains_examples", "contains_definitions", "contains_formulas", "analysis_method"]
        missing_keys = [key for key in required_keys if key not in result]
        
        if missing_keys:
            print(f"⚠️ Missing keys: {missing_keys}")
        else:
            print("✅ All required keys present")
            
        # Validate semantic_tags structure
        semantic_tags = result.get("semantic_tags", [])
        if semantic_tags:
            for tag in semantic_tags:
                if not isinstance(tag, dict) or "type" not in tag or "confidence" not in tag:
                    print(f"⚠️ Invalid tag structure: {tag}")
                else:
                    confidence = tag.get("confidence", 0)
                    if not (0.0 <= confidence <= 1.0):
                        print(f"⚠️ Invalid confidence value: {confidence}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Chạy tất cả tests"""
    print("🚀 Starting Semantic System Tests...")
    print("=" * 50)
    
    # Test health check trước
    test_health_check()
    
    # Test semantic analysis API
    test_semantic_analysis_api()
    
    # Test semantic search API (chỉ test nếu có data)
    print("\n⚠️ Note: Semantic search tests require existing textbook data in Qdrant")
    test_semantic_search_api()
    
    # Test service trực tiếp
    print("\n🔬 Testing service directly...")
    asyncio.run(test_semantic_analysis_service())
    
    print("\n" + "=" * 50)
    print("🏁 Tests completed!")
    print("\n📝 Next steps:")
    print("1. Upload some textbooks using /process-textbook-async")
    print("2. Test semantic search with real data")
    print("3. Compare old vs new semantic analysis results")

if __name__ == "__main__":
    main()
