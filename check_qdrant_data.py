#!/usr/bin/env python3
"""
Check Qdrant collections and data
"""

import requests
import json

def check_qdrant_collections():
    """Check what collections exist in Qdrant"""
    print("=== CHECKING QDRANT COLLECTIONS ===")
    
    try:
        # Direct Qdrant API call
        response = requests.get("http://localhost:6333/collections")
        
        if response.status_code == 200:
            data = response.json()
            collections = data.get('result', {}).get('collections', [])
            
            print(f"Found {len(collections)} collections:")
            for collection in collections:
                name = collection.get('name', 'Unknown')
                vectors_count = collection.get('vectors_count', 0)
                print(f"  - {name}: {vectors_count} vectors")
                
            return collections
        else:
            print(f"❌ Failed to get collections: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error checking collections: {e}")
        return []

def check_collection_info(collection_name):
    """Get detailed info about a specific collection"""
    print(f"\n=== CHECKING COLLECTION: {collection_name} ===")
    
    try:
        response = requests.get(f"http://localhost:6333/collections/{collection_name}")
        
        if response.status_code == 200:
            data = response.json()
            result = data.get('result', {})
            
            print(f"Status: {result.get('status', 'Unknown')}")
            print(f"Vectors count: {result.get('vectors_count', 0)}")
            print(f"Indexed vectors: {result.get('indexed_vectors_count', 0)}")
            print(f"Points count: {result.get('points_count', 0)}")
            
            config = result.get('config', {})
            params = config.get('params', {})
            vectors = params.get('vectors', {})
            
            if isinstance(vectors, dict):
                print(f"Vector size: {vectors.get('size', 'Unknown')}")
                print(f"Distance: {vectors.get('distance', 'Unknown')}")
            
            return result
        else:
            print(f"❌ Failed to get collection info: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error checking collection: {e}")
        return None

def sample_collection_data(collection_name, limit=3):
    """Sample some data from a collection"""
    print(f"\n=== SAMPLING DATA FROM: {collection_name} ===")
    
    try:
        payload = {
            "limit": limit,
            "with_payload": True,
            "with_vector": False
        }
        
        response = requests.post(
            f"http://localhost:6333/collections/{collection_name}/points/scroll",
            json=payload
        )
        
        if response.status_code == 200:
            data = response.json()
            points = data.get('result', {}).get('points', [])
            
            print(f"Sample {len(points)} points:")
            for i, point in enumerate(points, 1):
                payload_data = point.get('payload', {})
                print(f"  Point {i}:")
                print(f"    ID: {point.get('id', 'Unknown')}")
                print(f"    Type: {payload_data.get('type', 'Unknown')}")
                print(f"    Lesson ID: {payload_data.get('lesson_id', 'Unknown')}")
                print(f"    Text: {payload_data.get('text', '')[:100]}...")
                print(f"    Semantic Tags: {payload_data.get('semantic_tags', [])}")
                
        else:
            print(f"❌ Failed to sample data: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error sampling data: {e}")

def check_textbooks_in_mongodb():
    """Check what textbooks are available in MongoDB"""
    print("\n=== CHECKING TEXTBOOKS IN MONGODB ===")
    
    try:
        response = requests.get("http://localhost:8000/api/v1/pdf/textbooks")
        
        if response.status_code == 200:
            data = response.json()
            textbooks = data.get('textbooks', [])
            
            print(f"Found {len(textbooks)} textbooks:")
            for book in textbooks:
                book_id = book.get('_id', 'Unknown')
                title = book.get('title', 'Unknown')
                print(f"  - {book_id}: {title}")
                
            return textbooks
        else:
            print(f"❌ Failed to get textbooks: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error checking textbooks: {e}")
        return []

def main():
    """Main check function"""
    print("🔧 Checking Qdrant Data and Collections")
    print("=" * 50)
    
    # Check collections
    collections = check_qdrant_collections()
    
    # Check each collection
    for collection in collections:
        collection_name = collection.get('name')
        if collection_name:
            check_collection_info(collection_name)
            sample_collection_data(collection_name)
    
    # Check textbooks in MongoDB
    textbooks = check_textbooks_in_mongodb()
    
    print("\n" + "=" * 50)
    print("🏁 Data check completed!")
    
    # Summary
    print(f"\nSUMMARY:")
    print(f"- Qdrant collections: {len(collections)}")
    print(f"- MongoDB textbooks: {len(textbooks) if textbooks else 0}")
    
    if not collections:
        print("\n⚠️  NO COLLECTIONS FOUND!")
        print("   You need to process textbooks first to create vector data.")
        print("   Use the textbook processing endpoint to import data.")

if __name__ == "__main__":
    main()
